/* Copyright (c) 2023 Renmin University of China
RMDB is licensed under Mulan PSL v2.
You can use this software according to the terms and conditions of the Mulan PSL v2.
You may obtain a copy of Mulan PSL v2 at:
        http://license.coscl.org.cn/MulanPSL2
THIS SOFTWARE IS PROVIDED ON AN "AS IS" BASIS, WITHOUT WARRANTIES OF ANY KIND,
EITHER EXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO NON-INFRINGEMENT,
MERCHANTABILITY OR FIT FOR A PARTICULAR PURPOSE.
See the Mulan PSL v2 for more details. */

#pragma once
#include "execution_defs.h"
#include "execution_manager.h"
#include "executor_abstract.h"
#include "index/ix.h"
#include "system/sm.h"

class InsertExecutor : public AbstractExecutor {
private:
    TabMeta tab_;                   // 表的元数据
    std::vector<Value> values_;     // 需要插入的数据
    RmFileHandle* fh_;              // 表的数据文件句柄
    std::string tab_name_;          // 表名称
    Rid rid_;                       // 插入的位置，插入后赋值
    SmManager* sm_manager_;

public:
    InsertExecutor(SmManager* sm_manager, const std::string& tab_name, std::vector<Value> values, Context* context)
        : sm_manager_(sm_manager), tab_name_(tab_name), values_(std::move(values)) {
        tab_ = sm_manager_->db_.get_table(tab_name);
        if (values_.size() != tab_.cols.size()) {
            throw InvalidValueCountError();
        }
        fh_ = sm_manager_->fhs_.at(tab_name).get();
        context_ = context;
    }

    std::unique_ptr<RmRecord> Next() override {
        // 构建记录缓冲区
        const auto& file_hdr = fh_->get_file_hdr();
        RmRecord rec(file_hdr.record_size);

        // 填充记录数据
        for (size_t i = 0; i < values_.size(); ++i) {
            const auto& col = tab_.cols[i];
            auto& val = values_[i];

            // 类型转换处理
            if (col.type != val.type) {
                if ((col.type == TYPE_STRING && val.type != TYPE_STRING) ||
                    (col.type != TYPE_STRING && val.type == TYPE_STRING)) {
                    throw IncompatibleTypeError(coltype2str(col.type), coltype2str(val.type));
                } else if (col.type == TYPE_INT && val.type == TYPE_FLOAT) {
                    val.type = TYPE_INT;
                    val.set_int(static_cast<int>(val.float_val));
                } else {
                    val.type = TYPE_FLOAT;
                    val.set_float(static_cast<float>(val.int_val));
                }
            }

            val.init_raw(col.len);
            memcpy(rec.data + col.offset, val.raw->data, col.len);
        }

        // 预检查并构建索引键
        std::vector<std::pair<IxIndexHandle*, std::unique_ptr<char[]>>> pending_keys;
        pending_keys.reserve(tab_.indexes.size());  // 预留空间避免扩容

        for (const auto& index : tab_.indexes) {
            const std::string index_name = sm_manager_->get_ix_manager()->get_index_name(tab_name_, index.cols);
            auto* ih = sm_manager_->ihs_.at(index_name).get();
            
            auto key = std::make_unique<char[]>(index.col_tot_len);
            int offset = 0;
            for (int i = 0; i < index.col_num; ++i) {
                const auto& col = index.cols[i];
                memcpy(key.get() + offset, rec.data + col.offset, col.len);
                offset += col.len;
            }

            // 索引预插入检查
            ih->insert_entry(key.get(), Rid{-1, -1}, context_->txn_);  // 临时测试插入
            ih->delete_entry(key.get(), context_->txn_);               // 清理临时记录
            pending_keys.emplace_back(ih, std::move(key));
        }

        // 正式插入记录
        rid_ = fh_->insert_record(rec.data, context_);

        // 插入真实索引记录
        for (auto& [ih, key] : pending_keys) {
            ih->insert_entry(key.get(), rid_, context_->txn_);
        }

        // 记录事务写操作
        auto* wr = new WriteRecord(WType::INSERT_TUPLE, tab_name_, rid_, rec);
        context_->txn_->append_write_record(wr);

        return nullptr;
    }

    Rid& rid() override { return rid_; }
};
