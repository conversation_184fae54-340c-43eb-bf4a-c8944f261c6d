/* Copyright (c) 2023 Renmin University of China
RMDB is licensed under Mulan PSL v2.
You can use this software according to the terms and conditions of the Mulan PSL v2.
You may obtain a copy of Mulan PSL v2 at:
        http://license.coscl.org.cn/MulanPSL2
THIS SOFTWARE IS PROVIDED ON AN "AS IS" BASIS, WITHOUT WARRANTIES OF ANY KIND,
EITHER EXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO NON-INFRINGEMENT,
MERCHANTABILITY OR FIT FOR A PARTICULAR PURPOSE.
See the Mulan PSL v2 for more details. */

#pragma once
#include "execution_defs.h"
#include "execution_manager.h"
#include "executor_abstract.h"
#include "index/ix.h"
#include "system/sm.h"

class UpdateExecutor : public AbstractExecutor {
   private:
    TabMeta tab_;
    std::vector<Condition> conds_;
    RmFileHandle *fh_;
    std::vector<Rid> rids_;
    std::string tab_name_;
    std::vector<SetClause> set_clauses_;
    SmManager *sm_manager_;

   public:
    UpdateExecutor(SmManager *sm_manager, const std::string &tab_name, std::vector<SetClause> set_clauses,
                   std::vector<Condition> conds, std::vector<Rid> rids, Context *context) {
        sm_manager_ = sm_manager;
        tab_name_ = tab_name;
        set_clauses_ = set_clauses;
        tab_ = sm_manager_->db_.get_table(tab_name);
        fh_ = sm_manager_->fhs_.at(tab_name).get();
        conds_ = conds;
        rids_ = rids;
        context_ = context;
    }
    std::unique_ptr<RmRecord> Next() override {
        // 1. 提取本次更新涉及的列名
        std::unordered_set<std::string> updated_cols;
        for (auto &clause : set_clauses_) {
            updated_cols.insert(clause.lhs.col_name);
        }

        // 2. 遍历所有要更新的元组
        for (const auto &rid : rids_) {
            // 获取当前元组并初始化待更新记录
            auto target_record = fh_->get_record(rid, context_);
            RmRecord new_record(fh_->get_file_hdr().record_size);
            new_record.SetData(target_record->data);

            // 3. 更新新 record 中的字段值
            for (const auto &inner_rid : rids_) {
                /* 2.1 构造新的元组 */
                for (const auto &clause : set_clauses_) {
                    const TabCol &lhs_col = clause.lhs;
                    const SetValue &rvalue = clause.rhs;
                    Value rhs_val;
                    const auto &col_meta = tab_.get_col(lhs_col.col_name);

                    if (rvalue.is_expr) {
                        // 处理表达式赋值
                        TabCol ref_col;
                        Value col_val;
                        Value expr_result;
                        bool first_is_value = false;

                        // 解析表达式左右操作数
                        if (rvalue.first_is_col && rvalue.second_is_val) {
                            ref_col = rvalue.first_col;
                            rhs_val = rvalue.second_val;
                        } else if (rvalue.second_is_col && rvalue.first_is_val) {
                            ref_col = rvalue.second_col;
                            rhs_val = rvalue.first_val;
                            first_is_value = true;
                        }

                        // 从元组中获取参考列值
                        const auto &ref_col_meta = tab_.get_col(ref_col.col_name);
                        const char *ref_data = target_record->data + ref_col_meta->offset;

                        switch (ref_col_meta->type) {
                            case TYPE_INT:
                                col_val.set_int(*reinterpret_cast<const int*>(ref_data));
                                break;
                            case TYPE_FLOAT:
                                col_val.set_float(*reinterpret_cast<const float*>(ref_data));
                                break;
                            case TYPE_STRING:
                                col_val.set_str(std::string(ref_data, ref_col_meta->len));
                                break;
                            default:
                                throw InternalError("Invalid column type");
                        }
                        col_val.init_raw(ref_col_meta->len);

                        // 类型兼容性检查与转换
                        if (col_meta->type != col_val.type) {
                            if ((col_meta->type == TYPE_STRING && col_val.type != TYPE_STRING) ||
                                (col_meta->type == TYPE_INT && col_val.type == TYPE_FLOAT) ||
                                (col_meta->type != TYPE_STRING && col_val.type == TYPE_STRING)) {
                                throw IncompatibleTypeError(coltype2str(col_meta->type), 
                                                           coltype2str(col_val.type));
                            }
                            col_val.type = TYPE_FLOAT;
                            col_val.set_float(static_cast<float>(col_val.int_val));
                        }

                        if (col_meta->type != rhs_val.type) {
                            if ((col_meta->type == TYPE_STRING && rhs_val.type != TYPE_STRING) ||
                                (col_meta->type == TYPE_INT && rhs_val.type == TYPE_FLOAT) ||
                                (col_meta->type != TYPE_STRING && rhs_val.type == TYPE_STRING)) {
                                throw IncompatibleTypeError(coltype2str(col_meta->type), 
                                                           coltype2str(rhs_val.type));
                            }
                            rhs_val.type = TYPE_FLOAT;
                            rhs_val.set_float(static_cast<float>(rhs_val.int_val));
                        }

                        // 计算表达式结果
                        switch (col_meta->type) {
                            case TYPE_INT:
                                switch (rvalue.op) {
                                    case OP_ADD: expr_result.set_int(col_val.int_val + rhs_val.int_val); break;
                                    case OP_SUB: 
                                        expr_result.set_int(first_is_value ? 
                                                           (rhs_val.int_val - col_val.int_val) : 
                                                           (col_val.int_val - rhs_val.int_val)); 
                                        break;
                                    case OP_MUL: expr_result.set_int(col_val.int_val * rhs_val.int_val); break;
                                    case OP_DIV: 
                                        expr_result.set_int(first_is_value ? 
                                                           (rhs_val.int_val / col_val.int_val) : 
                                                           (col_val.int_val / rhs_val.int_val)); 
                                        break;
                                    default: throw InternalError("Invalid operator for int type");
                                }
                                break;
                            case TYPE_FLOAT:
                                switch (rvalue.op) {
                                    case OP_ADD: expr_result.set_float(col_val.float_val + rhs_val.float_val); break;
                                    case OP_SUB: 
                                        expr_result.set_float(first_is_value ? 
                                                             (rhs_val.float_val - col_val.float_val) : 
                                                             (col_val.float_val - rhs_val.float_val)); 
                                        break;
                                    case OP_MUL: expr_result.set_float(col_val.float_val * rhs_val.float_val); break;
                                    case OP_DIV: 
                                        expr_result.set_float(first_is_value ? 
                                                             (rhs_val.float_val / col_val.float_val) : 
                                                             (col_val.float_val / rhs_val.float_val)); 
                                        break;
                                    default: throw InternalError("Invalid operator for float type");
                                }
                                break;
                            case TYPE_STRING:
                                if (rvalue.op != OP_ADD) {
                                    throw InternalError("Only add operator supported for string type");
                                }
                                expr_result.set_str(col_val.str_val + rhs_val.str_val);
                                break;
                            default: throw InternalError("Invalid column type");
                        }

                        // 更新新记录
                        expr_result.init_raw(col_meta->len);
                        memcpy(new_record.data + col_meta->offset, expr_result.raw->data, col_meta->len);
                    } else {
                        // 处理直接赋值
                        rhs_val = rvalue.first_val;

                        // 类型兼容性检查与转换
                        if (col_meta->type != rhs_val.type) {
                            if ((col_meta->type == TYPE_STRING && rhs_val.type != TYPE_STRING) ||
                                (col_meta->type == TYPE_INT && rhs_val.type == TYPE_FLOAT) ||
                                (col_meta->type != TYPE_STRING && rhs_val.type == TYPE_STRING)) {
                                throw IncompatibleTypeError(coltype2str(col_meta->type), 
                                                           coltype2str(rhs_val.type));
                            }
                            rhs_val.type = TYPE_FLOAT;
                            rhs_val.set_float(static_cast<float>(rhs_val.int_val));
                        }

                        // 更新新记录
                        rhs_val.init_raw(col_meta->len);
                        memcpy(new_record.data + col_meta->offset, rhs_val.raw->data, col_meta->len);
                    }
                }
            }

            // 4. 构造新 key 并检查唯一性（仅检查受影响的索引）
            std::vector<std::unique_ptr<char[]>> new_keys;  // 缓存 key，后续重复用
            std::vector<IxIndexHandle *> related_indexes;   // 对应索引句柄

            for (auto &index : tab_.indexes) {
                // 检查索引是否涉及更新列
                bool is_related = false;
                for (auto &col : index.cols) {
                    if (updated_cols.count(col.name)) {
                        is_related = true;
                        break;
                    }
                }
                if (!is_related) continue;

                // 构造新索引键
                auto new_key = std::make_unique<char[]>(index.col_tot_len);
                int key_offset = 0;
                for (int j = 0; j < index.col_num; ++j) {
                    memcpy(new_key.get() + key_offset, 
                           new_record.data + index.cols[j].offset, 
                           index.cols[j].len);
                    key_offset += index.cols[j].len;
                }

                // 验证索引唯一性
                auto index_handle = sm_manager_->ihs_.at(
                    sm_manager_->get_ix_manager()->get_index_name(tab_name_, index.cols)).get();
                index_handle->insert_entry(new_key.get(), rid, context_->txn_);

                related_indexes.push_back(index_handle);
                new_keys.push_back(std::move(new_key));
            }

            // 5. 删除旧索引项（仅删除受影响的索引）
            for (size_t i = 0; i < related_indexes.size(); ++i) {
                auto index_handle = related_indexes[i];
                const auto &index_meta = tab_.indexes[i];

                // 构造旧索引键
                auto old_key = std::make_unique<char[]>(index_meta.col_tot_len);
                int key_offset = 0;
                for (int j = 0; j < index_meta.col_num; ++j) {
                    memcpy(old_key.get() + key_offset, 
                           target_record->data + index_meta.cols[j].offset, 
                           index_meta.cols[j].len);
                    key_offset += index_meta.cols[j].len;
                }

                index_handle->delete_entry(old_key.get(), context_->txn_);
            }

            // 6. 更新表记录
            fh_->update_record(rid, new_record.data, context_);

            // 7. 记录事务写操作
            auto write_rec = new WriteRecord(WType::UPDATE_TUPLE, tab_name_, rid, *target_record);
            context_->txn_->append_write_record(write_rec);
        }

        return nullptr;
    }

    Rid &rid() override { return _abstract_rid; }
};
