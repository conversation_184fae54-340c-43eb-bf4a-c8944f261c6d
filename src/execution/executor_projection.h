/* Copyright (c) 2023 Renmin University of China
RMDB is licensed under Mulan PSL v2.
You can use this software according to the terms and conditions of the Mulan PSL v2.
You may obtain a copy of Mulan PSL v2 at:
        http://license.coscl.org.cn/MulanPSL2
THIS SOFTWARE IS PROVIDED ON AN "AS IS" BASIS, WITHOUT WARRANTIES OF ANY KIND,
EITHER EXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO NON-INFRINGEMENT,
MERCHANTABILITY OR FIT FOR A PARTICULAR PURPOSE.
See the Mulan PSL v2 for more details. */

#pragma once
#include "execution_defs.h"
#include "execution_manager.h"
#include "executor_abstract.h"
#include "index/ix.h"
#include "system/sm.h"

class ProjectionExecutor : public AbstractExecutor {
private:
    std::unique_ptr<AbstractExecutor> prev_;  // 投影节点的子执行器
    std::vector<ColMeta> cols_;               // 需要投影的字段
    size_t len_ = 0;                          // 字段总长度
    std::vector<size_t> sel_idxs_;            // 选中列在子执行器结果中的索引

public:
    ProjectionExecutor(std::unique_ptr<AbstractExecutor> prev, const std::vector<TabCol>& sel_cols)
        : prev_(std::move(prev)) {
        size_t curr_offset = 0;
        const auto& prev_cols = prev_->cols();  // 缓存子执行器的列信息

        for (const auto& sel_col : sel_cols) {
            // 查找目标列在子执行器结果中的位置
            const auto pos = get_col(prev_cols, sel_col);
            sel_idxs_.push_back(std::distance(prev_cols.begin(), pos));

            // 构造投影后的列元数据
            ColMeta col = *pos;
            col.offset = curr_offset;
            curr_offset += col.len;
            cols_.push_back(col);
        }

        len_ = curr_offset;
    }

    void beginTuple() override {
        prev_->beginTuple();  // 移动子执行器到第一个元组
    }

    void nextTuple() override {
        prev_->nextTuple();  // 移动子执行器到下一个元组
    }

    std::unique_ptr<RmRecord> Next() override {
        // 从子执行器获取元组
        if (auto record = prev_->Next()) {
            // 构建投影后的新元组
            auto new_record = std::make_unique<RmRecord>(len_);
            size_t offset = 0;
            const auto& prev_cols = prev_->cols();  // 缓存子执行器的列信息

            for (size_t idx : sel_idxs_) {
                const ColMeta& col = prev_cols[idx];
                memcpy(new_record->data + offset, record->data + col.offset, col.len);
                offset += col.len;
            }

            return new_record;
        }

        return nullptr;
    }

    const std::vector<ColMeta>& cols() const override {
        return cols_;
    }

    size_t tupleLen() const override {
        return len_;
    }

    bool is_end() const override {
        return prev_->is_end();
    }

    Rid& rid() override {
        return _abstract_rid;
    }
};
    