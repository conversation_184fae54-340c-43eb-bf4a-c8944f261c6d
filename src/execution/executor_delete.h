/* Copyright (c) 2023 Renmin University of China
RMDB is licensed under Mulan PSL v2.
You can use this software according to the terms and conditions of the Mulan PSL v2.
You may obtain a copy of Mulan PSL v2 at:
        http://license.coscl.org.cn/MulanPSL2
THIS SOFTWARE IS PROVIDED ON AN "AS IS" BASIS, WITHOUT WARRANTIES OF ANY KIND,
EITHER EXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO NON-INFRINGEMENT,
MERCHANTABILITY OR FIT FOR A PARTICULAR PURPOSE.
See the Mulan PSL v2 for more details. */

#pragma once
#include "execution_defs.h"
#include "execution_manager.h"
#include "executor_abstract.h"
#include "index/ix.h"
#include "system/sm.h"

class DeleteExecutor : public AbstractExecutor {
   private:
    TabMeta tab_;                   // 表的元数据
    std::vector<Condition> conds_;  // delete的条件
    RmFileHandle *fh_;              // 表的数据文件句柄
    std::vector<Rid> rids_;         // 需要删除的记录的位置
    std::string tab_name_;          // 表名称
    SmManager *sm_manager_;

   public:
    DeleteExecutor(SmManager *sm_manager, const std::string &tab_name, std::vector<Condition> conds,
                   std::vector<Rid> rids, Context *context) 
        : sm_manager_(sm_manager), tab_name_(tab_name), conds_(std::move(conds)), rids_(std::move(rids)) {
        tab_ = sm_manager_->db_.get_table(tab_name);
        fh_ = sm_manager_->fhs_.at(tab_name).get();
        context_ = context;
    }

    std::unique_ptr<RmRecord> Next() override {
        // 一次性删除所有符合条件的元组
        for (const Rid& rid : rids_) {
            // 获取待删除的记录
            auto record = fh_->get_record(rid, context_);
            if (!record) continue;  // 确保记录存在

            // 从表中删除记录
            fh_->delete_record(rid, context_);

            // 删除相关索引
            for (const auto& index : tab_.indexes) {
                // 获取索引句柄
                const std::string index_name = sm_manager_->get_ix_manager()->get_index_name(tab_name_, index.cols);
                auto* ih = sm_manager_->ihs_.at(index_name).get();

                // 构造索引键
                char* key = new char[index.col_tot_len];
                int offset = 0;
                for (const auto& col : index.cols) {
                    memcpy(key + offset, record->data + col.offset, col.len);
                    offset += col.len;
                }

                // 执行索引删除
                ih->delete_entry(key, context_->txn_);
                delete[] key;  // 及时释放动态分配的内存
            }

            // 记录写操作
            auto* wr = new WriteRecord(WType::DELETE_TUPLE, tab_name_, rid, *record);
            context_->txn_->append_write_record(wr);
        }

        return nullptr;
    }

    Rid& rid() override { return _abstract_rid; }
};
