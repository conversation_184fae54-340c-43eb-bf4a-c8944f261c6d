/* A Bison parser, made by GNU Bison 3.8.2.  */

/* Bison interface for Yacc-like parsers in C

   Copyright (C) 1984, 1989-1990, 2000-2015, 2018-2021 Free Software Foundation,
   Inc.

   This program is free software: you can redistribute it and/or modify
   it under the terms of the GNU General Public License as published by
   the Free Software Foundation, either version 3 of the License, or
   (at your option) any later version.

   This program is distributed in the hope that it will be useful,
   but WITHOUT ANY WARRANTY; without even the implied warranty of
   MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
   GNU General Public License for more details.

   You should have received a copy of the GNU General Public License
   along with this program.  If not, see <https://www.gnu.org/licenses/>.  */

/* As a special exception, you may create a larger work that contains
   part or all of the Bison parser skeleton and distribute that work
   under terms of your choice, so long as that work isn't itself a
   parser generator using the skeleton or a modified version thereof
   as a parser skeleton.  Alternatively, if you modify or redistribute
   the parser skeleton itself, you may (at your option) remove this
   special exception, which will cause the skeleton and the resulting
   Bison output files to be licensed under the GNU General Public
   License without this special exception.

   This special exception was added by the Free Software Foundation in
   version 2.2 of Bison.  */

/* DO NOT RELY ON FEATURES THAT ARE NOT DOCUMENTED in the manual,
   especially those whose name start with YY_ or yy_.  They are
   private implementation details that can be changed or removed.  */

#ifndef YY_YY_ROOT_DBMS3_1_DB2025_X1_SRC_PARSER_YACC_TAB_H_INCLUDED
# define YY_YY_ROOT_DBMS3_1_DB2025_X1_SRC_PARSER_YACC_TAB_H_INCLUDED
/* Debug traces.  */
#ifndef YYDEBUG
# define YYDEBUG 0
#endif
#if YYDEBUG
extern int yydebug;
#endif

/* Token kinds.  */
#ifndef YYTOKENTYPE
# define YYTOKENTYPE
  enum yytokentype
  {
    YYEMPTY = -2,
    YYEOF = 0,                     /* "end of file"  */
    YYerror = 256,                 /* error  */
    YYUNDEF = 257,                 /* "invalid token"  */
    SHOW = 258,                    /* SHOW  */
    TABLES = 259,                  /* TABLES  */
    CREATE = 260,                  /* CREATE  */
    TABLE = 261,                   /* TABLE  */
    DROP = 262,                    /* DROP  */
    DESC = 263,                    /* DESC  */
    INSERT = 264,                  /* INSERT  */
    INTO = 265,                    /* INTO  */
    VALUES = 266,                  /* VALUES  */
    DELETE = 267,                  /* DELETE  */
    FROM = 268,                    /* FROM  */
    ASC = 269,                     /* ASC  */
    EXPLAIN = 270,                 /* EXPLAIN  */
    ORDER = 271,                   /* ORDER  */
    BY = 272,                      /* BY  */
    ON = 273,                      /* ON  */
    SEMI = 274,                    /* SEMI  */
    COUNT = 275,                   /* COUNT  */
    MIN = 276,                     /* MIN  */
    MAX = 277,                     /* MAX  */
    AVG = 278,                     /* AVG  */
    SUM = 279,                     /* SUM  */
    GROUP = 280,                   /* GROUP  */
    HAVING = 281,                  /* HAVING  */
    AS = 282,                      /* AS  */
    LOAD = 283,                    /* LOAD  */
    WHERE = 284,                   /* WHERE  */
    UPDATE = 285,                  /* UPDATE  */
    SET = 286,                     /* SET  */
    SELECT = 287,                  /* SELECT  */
    INT = 288,                     /* INT  */
    CHAR = 289,                    /* CHAR  */
    FLOAT = 290,                   /* FLOAT  */
    INDEX = 291,                   /* INDEX  */
    AND = 292,                     /* AND  */
    JOIN = 293,                    /* JOIN  */
    EXIT = 294,                    /* EXIT  */
    HELP = 295,                    /* HELP  */
    TXN_BEGIN = 296,               /* TXN_BEGIN  */
    TXN_COMMIT = 297,              /* TXN_COMMIT  */
    TXN_ABORT = 298,               /* TXN_ABORT  */
    TXN_ROLLBACK = 299,            /* TXN_ROLLBACK  */
    ORDER_BY = 300,                /* ORDER_BY  */
    ENABLE_NESTLOOP = 301,         /* ENABLE_NESTLOOP  */
    ENABLE_SORTMERGE = 302,        /* ENABLE_SORTMERGE  */
    STATIC_CHECKPOINT = 303,       /* STATIC_CHECKPOINT  */
    LIMIT = 304,                   /* LIMIT  */
    LEQ = 305,                     /* LEQ  */
    NEQ = 306,                     /* NEQ  */
    GEQ = 307,                     /* GEQ  */
    T_EOF = 308,                   /* T_EOF  */
    IDENTIFIER = 309,              /* IDENTIFIER  */
    VALUE_STRING = 310,            /* VALUE_STRING  */
    PATH_TOKEN = 311,              /* PATH_TOKEN  */
    VALUE_INT = 312,               /* VALUE_INT  */
    VALUE_FLOAT = 313,             /* VALUE_FLOAT  */
    VALUE_BOOL = 314               /* VALUE_BOOL  */
  };
  typedef enum yytokentype yytoken_kind_t;
#endif

/* Value type.  */

/* Location type.  */
#if ! defined YYLTYPE && ! defined YYLTYPE_IS_DECLARED
typedef struct YYLTYPE YYLTYPE;
struct YYLTYPE
{
  int first_line;
  int first_column;
  int last_line;
  int last_column;
};
# define YYLTYPE_IS_DECLARED 1
# define YYLTYPE_IS_TRIVIAL 1
#endif




int yyparse (void);


#endif /* !YY_YY_ROOT_DBMS3_1_DB2025_X1_SRC_PARSER_YACC_TAB_H_INCLUDED  */
