/* Copyright (c) 2023 Renmin University of China
RMDB is licensed under Mulan PSL v2.
You can use this software according to the terms and conditions of the Mulan PSL v2.
You may obtain a copy of Mulan PSL v2 at:
        http://license.coscl.org.cn/MulanPSL2
THIS SOFTWARE IS PROVIDED ON AN "AS IS" BASIS, WITHOUT WARRANTIES OF ANY KIND,
EITHER EXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO NON-INFRINGEMENT,
MERCHANTABILITY OR FIT FOR A PARTICULAR PURPOSE.
See the Mulan PSL v2 for more details. */

#pragma once

#include <map>
#include <unordered_map>
#include <unordered_set>
#include "log_manager.h"
#include "storage/disk_manager.h"
#include "system/sm_manager.h"

class RedoLogsInPage {
public:
    RedoLogsInPage() { table_file_ = nullptr; }
    RmFileHandle* table_file_;
    std::vector<lsn_t> redo_logs_;   // 在该page上需要redo的操作的lsn
};

class RecoveryManager {
public:
    RecoveryManager(DiskManager* disk_manager, BufferPoolManager* buffer_pool_manager, SmManager* sm_manager) {
        disk_manager_ = disk_manager;
        buffer_pool_manager_ = buffer_pool_manager;
        sm_manager_ = sm_manager;
    }

    void recover();                                                 // 主恢复函数
    void analyze();
    void redo();
    void undo();
    void reBuildIndex();

private:
    void analyze_checkpoint_record(int checkpoint_lsn);             // 分析检查点记录
    void analyze_log_from_lsn(int start_lsn);                      // 从指定LSN开始分析日志
    void read_and_parse_log(int start_lsn);                        // 一次性读取并解析日志
    void redo_transaction(int txn_id);                             // 重做指定事务
    void undo_transaction(int txn_id);                             // 撤销指定事务

    // REDO操作的具体实现
    void redo_insert_operation(const InsertLogRecord& log);        // 重做插入操作
    void redo_delete_operation(const DeleteLogRecord& log);        // 重做删除操作
    void redo_update_operation(const UpdateLogRecord& log);        // 重做更新操作

    // UNDO操作的具体实现
    void undo_insert_operation(const InsertLogRecord& log);        // 撤销插入操作
    void undo_delete_operation(const DeleteLogRecord& log);        // 撤销删除操作
    void undo_update_operation(const UpdateLogRecord& log);        // 撤销更新操作
    
    void recalculate_global_min_undo_lsn();
    LogBuffer buffer_;                                              // 读入日志
    DiskManager* disk_manager_;                                     // 用来读写文件
    BufferPoolManager* buffer_pool_manager_;                        // 对页面进行读写
    SmManager* sm_manager_;                                         // 访问数据库元数据

    // 恢复过程中的数据结构
    std::unordered_set<int> undo_list_;                            // 需要撤销的事务列表
    std::vector<int> redo_list_;                                   // 需要重做的事务列表（按提交顺序）
    std::unordered_map<int, int> txn_last_lsn_;                    // 事务的最后一条日志LSN
    int checkpoint_lsn_;                                           // 检查点LSN

    std::unordered_map<txn_id_t, lsn_t> txn_min_lsn_;

    lsn_t global_min_undo_lsn_{INVALID_LSN};
    // lsn_t max_lsn{INVALID_LSN};

    // 缓存的日志记录，避免重复读取日志文件
    std::vector<std::pair<LogType, std::vector<char>>> log_records_; // 缓存的日志记录
    std::unordered_map<int, lsn_t> txn_lsn;     // 事务ID到日志记录索引的映射
        

    std::vector<LogRecord> loglist;
    std::vector<std::pair<lsn_t, std::shared_ptr<LogRecord>>> logs;
    std::vector<int> log_index;

    std::unordered_set<std::string> tables_;
};